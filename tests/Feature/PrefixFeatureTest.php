<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\AccountType;

class PrefixFeatureTest extends TestCase
{
    /**
     * Test that AccountType model has the getPrefixedTitle method
     */
    public function test_account_type_has_get_prefixed_title_method()
    {
        $accountType = new AccountType();
        $this->assertTrue(method_exists($accountType, 'getPrefixedTitle'));
    }

    /**
     * Test getPrefixedTitle method returns original title when prefix is disabled
     */
    public function test_get_prefixed_title_returns_original_when_disabled()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = false;
        $accountType->title_prefix_format = 'Bài {number} - ';
        
        $originalTitle = 'Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);
        
        $this->assertEquals($originalTitle, $result);
    }

    /**
     * Test getPrefixedTitle method returns prefixed title when enabled
     */
    public function test_get_prefixed_title_returns_prefixed_when_enabled()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';
        
        $originalTitle = 'Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);
        
        $this->assertEquals('Bài 1 - Lớp 1 - Chương trình Hè', $result);
    }

    /**
     * Test getPrefixedTitle method with different position numbers (course-based)
     */
    public function test_get_prefixed_title_with_different_course_positions()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        $originalTitle = 'Test Material';

        // Test course-based numbering - each course starts from 1
        $result1 = $accountType->getPrefixedTitle($originalTitle, 1);
        $this->assertEquals('Bài 1 - Test Material', $result1);

        $result2 = $accountType->getPrefixedTitle($originalTitle, 2);
        $this->assertEquals('Bài 2 - Test Material', $result2);

        $result3 = $accountType->getPrefixedTitle($originalTitle, 3);
        $this->assertEquals('Bài 3 - Test Material', $result3);
    }

    /**
     * Test getPrefixedTitle method returns original title when format is empty
     */
    public function test_get_prefixed_title_returns_original_when_format_empty()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = '';

        $originalTitle = 'Test Material';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);

        $this->assertEquals($originalTitle, $result);
    }

    /**
     * Test getPrefixedTitle method strips existing "Bài X - " prefixes
     */
    public function test_get_prefixed_title_strips_existing_prefix()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        // Test with existing prefix
        $titleWithPrefix = 'Bài 5 - Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($titleWithPrefix, 1);
        $this->assertEquals('Bài 1 - Lớp 1 - Chương trình Hè', $result);

        // Test with different existing prefix format
        $titleWithSpaces = 'Bài  10  -  Test Material';
        $result2 = $accountType->getPrefixedTitle($titleWithSpaces, 3);
        $this->assertEquals('Bài 3 - Test Material', $result2);
    }

    /**
     * Test getPrefixedTitle method with titles without existing prefix
     */
    public function test_get_prefixed_title_without_existing_prefix()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        $originalTitle = 'Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);

        $this->assertEquals('Bài 1 - Lớp 1 - Chương trình Hè', $result);
    }

    /**
     * Test getPrefixedTitle method ensures proper spacing
     */
    public function test_get_prefixed_title_ensures_proper_spacing()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;

        // Test with prefix format that doesn't end with space
        $accountType->title_prefix_format = 'Bài {number}';
        $result = $accountType->getPrefixedTitle('Test Material', 1);
        $this->assertEquals('Bài 1 Test Material', $result);

        // Test with prefix format that ends with dash (should add space)
        $accountType->title_prefix_format = 'Bài {number}-';
        $result2 = $accountType->getPrefixedTitle('Test Material', 2);
        $this->assertEquals('Bài 2- Test Material', $result2);

        // Test with prefix format that ends with space
        $accountType->title_prefix_format = 'Bài {number} ';
        $result3 = $accountType->getPrefixedTitle('Test Material', 3);
        $this->assertEquals('Bài 3 Test Material', $result3);
    }

    /**
     * Test getPrefixedTitle method replaces numbered patterns with prefix only
     */
    public function test_get_prefixed_title_replaces_numbered_patterns()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        // Test various numbered patterns - should be replaced with just prefix (no dash)
        $result1 = $accountType->getPrefixedTitle('Bài 3', 1);
        $this->assertEquals('Bài 1', $result1);

        $result2 = $accountType->getPrefixedTitle('Chuyên đề 5', 2);
        $this->assertEquals('Bài 2', $result2);

        $result3 = $accountType->getPrefixedTitle('Hè 10', 3);
        $this->assertEquals('Bài 3', $result3);

        // Test other patterns
        $result4 = $accountType->getPrefixedTitle('Lesson 7', 4);
        $this->assertEquals('Bài 4', $result4);

        $result5 = $accountType->getPrefixedTitle('Unit 15', 5);
        $this->assertEquals('Bài 5', $result5);

        $result6 = $accountType->getPrefixedTitle('Chapter 2', 6);
        $this->assertEquals('Bài 6', $result6);

        // Test with different prefix format
        $accountType->title_prefix_format = 'Bài {number}';
        $result7 = $accountType->getPrefixedTitle('Module 8', 7);
        $this->assertEquals('Bài 7', $result7);
    }

    /**
     * Test that regular titles with content are not affected by numbered pattern logic
     */
    public function test_get_prefixed_title_preserves_regular_titles()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        // These should NOT be treated as numbered patterns
        $result1 = $accountType->getPrefixedTitle('Bài 3 - Toán học', 1);
        $this->assertEquals('Bài 1 - Toán học', $result1);

        $result2 = $accountType->getPrefixedTitle('Chuyên đề 1 - Văn học', 2);
        $this->assertEquals('Bài 2 - Chuyên đề 1 - Văn học', $result2);

        $result3 = $accountType->getPrefixedTitle('Hè 2 - Hoạt động', 3);
        $this->assertEquals('Bài 3 - Hè 2 - Hoạt động', $result3);
    }
}
